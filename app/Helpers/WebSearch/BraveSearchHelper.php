<?php

namespace App\Helpers\WebSearch;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BraveSearchHelper
{
    public const SUPPORTED_COUNTRY_CODES = [
        'AR', // Argentina
        'AU', // Australia
        'AT', // Austria
        'BE', // Belgium
        'BR', // Brazil
        'CA', // Canada
        'CL', // Chile
        'DK', // Denmark
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'HK', // Hong Kong
        'IN', // India
        'ID', // Indonesia
        'IT', // Italy
        'JP', // Japan
        'KR', // South Korea
        'MY', // Malaysia
        'MX', // Mexico
        'NL', // Netherlands
        'NZ', // New Zealand
        'NO', // Norway
        'CN', // China
        'PL', // Poland
        'PT', // Portugal
        'PH', // Philippines
        'RU', // Russia
        'SA', // Saudi Arabia
        'ZA', // South Africa
        'ES', // Spain
        'SE', // Sweden
        'CH', // Switzerland
        'TW', // Taiwan
        'TR', // Turkey
        'GB', // United Kingdom
        'US', // United States
    ];

    protected string $apiKey;

    protected string $baseUrl = 'https://api.search.brave.com/res/v1/web/search';

    /**
     * BraveSearchHelper constructor.
     *
     * @param  string  $apiKey  Your Brave Search API subscription token.
     *
     * @throws Exception If the API key is not provided.
     */
    public function __construct(string $apiKey)
    {
        if (empty($apiKey)) {
            Log::error('[BraveSearchHelper] API key is required but not provided.');
            throw new Exception('Brave Search API key (X-Subscription-Token) is required.');
        }
        $this->apiKey = $apiKey;
    }

    /**
     * Performs a search using the Brave Search API.
     *
     * @param  string  $query  The search query.
     * @param  array  $additionalParams  Additional parameters for the API request.
     *                                   Supported keys (refer to Brave Search API docs for specifics):
     *                                   'country' => e.g., 'US', 'GB', 'DE'
     *                                   'count' => Number of results to return.
     *                                   'offset' => Offset for pagination.
     *                                   'safesearch' => e.g., 'off', 'moderate', 'strict'
     *                                   'result_filter' => Comma-separated types, e.g., 'web,videos,news'
     *                                   'freshness' => (If supported by API) e.g., 'pd:7' for past 7 days, 'pm:1' for past month
     *                                   'spellcheck' => (If supported by API) 'on' or 'off'
     * @return array|null The API response as an array, or null on failure.
     */
    public function search(string $query, array $additionalParams = []): ?array
    {
        if (empty($query)) {
            Log::error('[BraveSearchHelper] Search query cannot be empty.');

            return null;
        }

        if (isset($additionalParams['country']) &&
            ! empty($additionalParams['country']) &&
            ! in_array(strtoupper($additionalParams['country']), self::SUPPORTED_COUNTRY_CODES)) {
            Log::warning('[BraveSearchHelper] Provided country code is not in the predefined list of supported codes. Proceeding with the request.', [
                'query' => $query,
                'provided_country_code' => $additionalParams['country'],
            ]);
        }

        $defaultParams = ['q' => $query];
        $params = array_merge($defaultParams, $additionalParams);

        // Ensure all parameters are properly typed to avoid array conversion issues
        $cleanParams = [];
        foreach ($params as $key => $value) {
            if (is_array($value)) {
                // Convert arrays to comma-separated strings if needed
                $cleanParams[$key] = implode(',', $value);
                Log::warning('[BraveSearchHelper] Array parameter converted to string.', [
                    'parameter' => $key,
                    'original_value' => $value,
                    'converted_value' => $cleanParams[$key],
                ]);
            } else {
                // Keep numeric parameters as integers/floats, others as strings
                if (in_array($key, ['count', 'offset']) && is_numeric($value)) {
                    $cleanParams[$key] = (int) $value;
                } else {
                    $cleanParams[$key] = (string) $value;
                }
            }
        }

        try {
            Log::debug('[BraveSearchHelper] Making API request.', [
                'url' => $this->baseUrl,
                'params' => array_diff_key($cleanParams, ['api_key' => '']), // Exclude API key from logs
            ]);

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'X-Subscription-Token' => $this->apiKey,
            ])->get($this->baseUrl, $cleanParams);

            if ($response->successful()) {
                $result = $response->json();

                Log::debug('[BraveSearchHelper] Successful API response.', [
                    'query' => $query,
                    'response_keys' => array_keys($result ?? []),
                    'web_results_count' => isset($result['web']['results']) ? count($result['web']['results']) : 0,
                    'mixed_structure' => isset($result['mixed']) ? $this->debugMixedStructure($result['mixed']) : null,
                    'full_response_preview' => json_encode($result, JSON_PRETTY_PRINT),
                ]);

                return $result;
            }

            $errorMessage = 'Brave API request failed.';
            $responseBody = $response->body();
            $errorDetails = null;

            // Safely attempt to get JSON error details
            try {
                $errorDetails = $response->json();
            } catch (Exception $jsonException) {
                Log::debug('[BraveSearchHelper] Response is not valid JSON.', [
                    'json_error' => $jsonException->getMessage(),
                ]);
            }

            if ($errorDetails && (isset($errorDetails['message']) || isset($errorDetails['error']))) {
                $errorField = $errorDetails['message'] ?? $errorDetails['error'];
                // Handle both string and array error messages
                if (is_array($errorField)) {
                    $errorMessage .= ' API Error: '.json_encode($errorField);
                } else {
                    $errorMessage .= ' API Error: '.$errorField;
                }
            } elseif (is_string($responseBody) && ! empty($responseBody)) {
                $errorMessage .= ' Response: '.substr($responseBody, 0, 200); // Log part of non-JSON response
            }

            Log::error('[BraveSearchHelper] '.$errorMessage, [
                'status' => $response->status(),
                'query_params' => array_diff_key($cleanParams, ['api_key' => '']), // Exclude API key from logs
                'response_body' => substr($responseBody, 0, 500), // Limit response body size in logs
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('[BraveSearchHelper] Exception during API request.', [
                'message' => $e->getMessage(),
                'query' => $query,
                'params' => array_diff_key($cleanParams ?? [], ['api_key' => '']),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Retrieves the web search results from an API response.
     * The exact path might depend on the 'result_filter' used and API version.
     * Common path: $apiResponse['web']['results']
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null Array of web search results or null if not found/on error.
     */
    public function getWebResults(array $apiResponse): ?array
    {
        return $apiResponse['web']['results'] ?? null;
    }

    /**
     * Retrieves video results from an API response.
     * Common path: $apiResponse['videos']['results']
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null Array of video results or null.
     */
    public function getVideos(array $apiResponse): ?array
    {
        return $apiResponse['videos']['results'] ?? null;
    }

    /**
     * Retrieves news results from an API response.
     * Common path: $apiResponse['news']['results']
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null Array of news results or null.
     */
    public function getNews(array $apiResponse): ?array
    {
        return $apiResponse['news']['results'] ?? null;
    }

    /**
     * Retrieves discussions results from an API response.
     * Common path: $apiResponse['discussions']['results']
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null Array of discussions or null.
     */
    public function getDiscussions(array $apiResponse): ?array
    {
        return $apiResponse['discussions']['results'] ?? null;
    }

    /**
     * Retrieves FAQ results from an API response.
     * Common path: $apiResponse['faq']['results']
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null Array of FAQs or null.
     */
    public function getFaqs(array $apiResponse): ?array
    {
        return $apiResponse['faq']['results'] ?? null;
    }

    /**
     * Retrieves the query information object from an API response.
     *
     * @param  array  $apiResponse  The full API response array.
     * @return array|null The query object or null.
     */
    public function getQueryInfo(array $apiResponse): ?array
    {
        return $apiResponse['query'] ?? null;
    }

    /**
     * Debug the mixed structure from Brave Search API response.
     *
     * @param  array  $mixed  The mixed array from the response
     * @return array Debug information about the mixed structure
     */
    protected function debugMixedStructure(array $mixed): array
    {
        $debug = [];

        foreach ($mixed as $key => $value) {
            if (is_array($value)) {
                $debug[$key] = [
                    'type' => 'array',
                    'count' => count($value),
                    'keys' => array_keys($value),
                ];

                // Look for web results in different locations
                if (isset($value['web']) && is_array($value['web'])) {
                    $debug[$key]['web_structure'] = [
                        'keys' => array_keys($value['web']),
                        'results_count' => isset($value['web']['results']) ? count($value['web']['results']) : 0,
                    ];
                }

                // Look for results directly in the mixed item
                if (isset($value['results']) && is_array($value['results'])) {
                    $debug[$key]['direct_results_count'] = count($value['results']);
                }
            } else {
                $debug[$key] = [
                    'type' => gettype($value),
                    'value' => is_string($value) ? substr($value, 0, 100) : $value,
                ];
            }
        }

        return $debug;
    }
}

<?php

namespace App\Helpers\WebSearch;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BrightDataSerpHelper
{
    /**
     * List of supported country codes for both Google and Bing.
     * These are two-letter country codes used in the 'gl' parameter for Google
     * and 'cc' parameter for Bing.
     */
    public const SUPPORTED_COUNTRY_CODES = [
        'AR', // Argentina
        'AU', // Australia
        'AT', // Austria
        'BE', // Belgium
        'BR', // Brazil
        'CA', // Canada
        'CL', // Chile
        'DK', // Denmark
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'HK', // Hong Kong
        'IN', // India
        'ID', // Indonesia
        'IT', // Italy
        'JP', // Japan
        'KR', // South Korea
        'MY', // Malaysia
        'MX', // Mexico
        'NL', // Netherlands
        'NZ', // New Zealand
        'NO', // Norway
        'CN', // China
        'PL', // Poland
        'PT', // Portugal
        'PH', // Philippines
        'RU', // Russia
        'SA', // Saudi Arabia
        'ZA', // South Africa
        'ES', // Spain
        'SE', // Sweden
        'CH', // Switzerland
        'TW', // Taiwan
        'TR', // Turkey
        'GB', // United Kingdom
        'US', // United States
    ];

    /**
     * Bright Data API credentials
     */
    protected string $apiKey;

    protected string $customerId;

    protected string $zoneId;

    /**
     * Base URLs for the Bright Data SERP API
     */
    protected string $baseUrl = 'https://brightdata.com/api/serp';

    /**
     * Google domain mapping for country codes
     * Maps country codes to their respective Google domains
     */
    protected array $googleDomains = [
        'us' => 'google.com',
        'uk' => 'google.co.uk',
        'ca' => 'google.ca',
        'au' => 'google.com.au',
        'nz' => 'google.co.nz',
        'ie' => 'google.ie',
        'in' => 'google.co.in',
        'za' => 'google.co.za',
        'br' => 'google.com.br',
        'mx' => 'google.com.mx',
        'ar' => 'google.com.ar',
        'cl' => 'google.cl',
        'co' => 'google.com.co',
        'pe' => 'google.com.pe',
        'jp' => 'google.co.jp',
        'kr' => 'google.co.kr',
        'hk' => 'google.com.hk',
        'sg' => 'google.com.sg',
        'my' => 'google.com.my',
        'ph' => 'google.com.ph',
        'id' => 'google.co.id',
        'th' => 'google.co.th',
        'vn' => 'google.com.vn',
        'de' => 'google.de',
        'fr' => 'google.fr',
        'it' => 'google.it',
        'es' => 'google.es',
        'pt' => 'google.pt',
        'nl' => 'google.nl',
        'be' => 'google.be',
        'ch' => 'google.ch',
        'at' => 'google.at',
        'dk' => 'google.dk',
        'no' => 'google.no',
        'se' => 'google.se',
        'fi' => 'google.fi',
        'pl' => 'google.pl',
        'ru' => 'google.ru',
        'tr' => 'google.com.tr',
        'gr' => 'google.gr',
        'il' => 'google.co.il',
        'ae' => 'google.ae',
        'sa' => 'google.com.sa',
    ];

    /**
     * BrightDataSerpHelper constructor.
     *
     * @param  string  $apiKey  Your Bright Data API key
     * @param  string  $customerId  Your Bright Data customer ID (e.g., 'hl_a84e30e2')
     * @param  string  $zoneId  Your Bright Data zone ID (e.g., 'serp_api1')
     *
     * @throws Exception If any required credentials are not provided
     */
    public function __construct(string $apiKey, string $customerId, string $zoneId)
    {
        if (empty($apiKey)) {
            Log::error('[BrightDataSerpHelper] API key is required but not provided.');
            throw new Exception('Bright Data API key is required.');
        }

        if (empty($customerId)) {
            Log::error('[BrightDataSerpHelper] Customer ID is required but not provided.');
            throw new Exception('Bright Data customer ID is required.');
        }

        if (empty($zoneId)) {
            Log::error('[BrightDataSerpHelper] Zone ID is required but not provided.');
            throw new Exception('Bright Data zone ID is required.');
        }

        $this->apiKey = $apiKey;
        $this->customerId = $customerId;
        $this->zoneId = $zoneId;
    }

    /**
     * Performs a search using the Bright Data SERP API.
     *
     * @param  string  $engine  The search engine to use ('google' or 'bing')
     * @param  string  $query  The search query
     * @param  string  $countryCode  The country code for the search (e.g., 'US', 'GB')
     * @param  array  $additionalParams  Additional parameters for the API request
     *                                   Common parameters for both engines:
     *                                   - 'brd_mobile': Device type (0 for desktop, 1 for mobile, or specific values like 'ios', 'android')
     *
     *                               Google-specific parameters:
     *                               - 'num': Number of results to return (default: 100)
     *                               - 'tbs': Time-based search (default: 'qdr:d' for current day)
     *                               - 'hl': Language code (default: 'en')
     *                               - 'start': Pagination offset
     *                               - 'tbm': Search type ('isch' for images, 'shop' for shopping, 'nws' for news)
     *                               - 'uule': Geo-location parameter
     *
     *                               Bing-specific parameters:
     *                               - 'count': Number of results to return (default: 100)
     *                               - 'time': Time filter (default: 'day')
     *                               - 'first': Pagination offset
     *                               - 'mkt': Market (e.g., 'en-US')
     *                               - 'location': Location name (requires 'lat' and 'lon')
     *                               - 'lat': Latitude for geo-location
     *                               - 'lon': Longitude for geo-location
     * @return array|null The API response as an array, or null on failure
     */
    public function search(string $engine, string $query, string $countryCode = 'US', array $additionalParams = []): ?array
    {
        if (empty($query)) {
            Log::error('[BrightDataSerpHelper] Search query cannot be empty.');

            return null;
        }

        if (! in_array(strtolower($engine), ['google', 'bing'])) {
            Log::error('[BrightDataSerpHelper] Unsupported search engine. Only "google" and "bing" are supported.');

            return null;
        }

        if (! in_array(strtoupper($countryCode), self::SUPPORTED_COUNTRY_CODES)) {
            Log::warning('[BrightDataSerpHelper] Provided country code is not in the predefined list of supported codes. Proceeding with the request.', [
                'query' => $query,
                'provided_country_code' => $countryCode,
            ]);
        }

        try {
            // Build the search URL with parameters
            $searchUrl = $this->buildSearchUrl($engine, $query, $countryCode, $additionalParams);

            // Build proxy authentication string
            $proxyAuth = $this->customerId.':'.$this->apiKey;
            $proxyHost = 'brd.superproxy.io:33335';

            Log::debug('[BrightDataSerpHelper] Making API request.', [
                'engine' => $engine,
                'query' => $query,
                'country_code' => $countryCode,
                'url' => $searchUrl,
            ]);

            // Make the request using HTTP proxy format as shown in Bright Data documentation
            $response = Http::withOptions([
                'proxy' => [
                    'http' => 'http://'.$proxyAuth.'@'.$proxyHost,
                    'https' => 'http://'.$proxyAuth.'@'.$proxyHost,
                ],
                'timeout' => 30,
                'verify' => false,
            ])->get($searchUrl);

            if (! $response->successful()) {
                $this->logError($engine, $response, $query, $searchUrl);

                return null;
            }

            // Parse the response based on whether brd_json=1 was used
            $result = $this->parseSearchResponse($response->body(), $additionalParams['brd_json'] ?? 1);

            // Add detailed debugging for response content
            Log::debug('[BrightDataSerpHelper] API response received.', [
                'engine' => $engine,
                'query' => $query,
                'response_status' => $response->status(),
                'response_length' => strlen($response->body()),
                'response_preview' => substr($response->body(), 0, 1000),
                'parsed_result_keys' => $result ? array_keys($result) : null,
                'organic_results_count' => isset($result['organic_results']) ? count($result['organic_results']) : 0,
            ]);

            if (! $result) {
                Log::error('[BrightDataSerpHelper] Failed to parse search response.', [
                    'engine' => $engine,
                    'query' => $query,
                    'response_length' => strlen($response->body()),
                    'response_body' => substr($response->body(), 0, 2000),
                ]);

                return null;
            }

            return $result;
        } catch (Exception $e) {
            Log::error('[BrightDataSerpHelper] Exception during API request.', [
                'engine' => $engine,
                'message' => $e->getMessage(),
                'query' => $query,
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Builds the search URL for the specified engine with parameters.
     *
     * @param  string  $engine  The search engine to use
     * @param  string  $query  The search query
     * @param  string  $countryCode  The country code
     * @param  array  $additionalParams  Additional parameters
     * @return string The complete search URL
     */
    protected function buildSearchUrl(string $engine, string $query, string $countryCode, array $additionalParams): string
    {
        $engine = strtolower($engine);
        $countryCode = strtolower($countryCode);

        // Build query parameters
        $params = ['q' => $query];

        // Add brd_json parameter for parsed JSON response
        $params['brd_json'] = $additionalParams['brd_json'] ?? 1;

        if ($engine === 'google') {
            // Google-specific parameters
            $params['gl'] = $countryCode;
            $params['hl'] = $additionalParams['hl'] ?? 'en';
            $params['num'] = $additionalParams['num'] ?? 100;
            $params['tbs'] = $additionalParams['tbs'] ?? 'qdr:d';
            $params['start'] = $additionalParams['start'] ?? 0;

            // Add mobile parameter if specified
            if (isset($additionalParams['brd_mobile'])) {
                $params['brd_mobile'] = $additionalParams['brd_mobile'];
            }

            // Build Google URL with domain
            $domain = $this->getGoogleDomain($countryCode);
            $baseUrl = "https://www.{$domain}/search";
        } else {
            // Bing-specific parameters
            $params['cc'] = $countryCode;
            $params['count'] = $additionalParams['count'] ?? 100;
            $params['first'] = $additionalParams['first'] ?? 1;
            $params['mkt'] = $additionalParams['mkt'] ?? 'en-US';

            // Add mobile parameter if specified
            if (isset($additionalParams['brd_mobile'])) {
                $params['brd_mobile'] = $additionalParams['brd_mobile'];
            }

            $baseUrl = 'https://www.bing.com/search';
        }

        return $baseUrl.'?'.http_build_query($params);
    }

    /**
     * Parses the search response from Bright Data.
     *
     * @param  string  $responseBody  The response body
     * @param  int  $brdJson  Whether brd_json=1 was used (1 for JSON, 0 for HTML)
     * @return array|null The parsed response or null on failure
     */
    protected function parseSearchResponse(string $responseBody, int $brdJson = 1): ?array
    {
        if ($brdJson === 1) {
            // Response should be JSON
            try {
                $data = json_decode($responseBody, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('[BrightDataSerpHelper] Failed to decode JSON response.', [
                        'json_error' => json_last_error_msg(),
                        'response_preview' => substr($responseBody, 0, 500),
                    ]);

                    return null;
                }

                return $data;
            } catch (Exception $e) {
                Log::error('[BrightDataSerpHelper] Exception while parsing JSON response.', [
                    'message' => $e->getMessage(),
                    'response_preview' => substr($responseBody, 0, 500),
                ]);

                return null;
            }
        } else {
            // Response is HTML - would need HTML parsing logic
            // For now, return the raw HTML in a structured format
            return [
                'html' => $responseBody,
                'organic_results' => [], // Would need to parse HTML to extract results
            ];
        }
    }

    /**
     * Logs an error from the API response.
     *
     * @param  string  $engine  The search engine used
     * @param  \Illuminate\Http\Client\Response  $response  The HTTP response
     * @param  string  $query  The search query
     * @param  string  $url  The request URL
     */
    protected function logError(string $engine, $response, string $query, string $url): void
    {
        $errorMessage = 'Bright Data API request failed.';
        $responseBody = $response->body();

        // Try to get JSON error details
        $errorDetails = null;
        try {
            $errorDetails = $response->json();
        } catch (Exception $e) {
            // Response is not JSON, ignore
        }

        if ($errorDetails && isset($errorDetails['error'])) {
            $errorMessage .= ' API Error: '.$errorDetails['error'];
        }

        Log::error('[BrightDataSerpHelper] '.$errorMessage, [
            'engine' => $engine,
            'status' => $response->status(),
            'query' => $query,
            'url' => $url,
            'response_body' => $responseBody,
        ]);
    }

    /**
     * Get the Google domain for a specific country code.
     *
     * @param  string  $countryCode  The country code
     * @return string|null The Google domain or null if not found
     */
    protected function getGoogleDomain(string $countryCode): ?string
    {
        $countryCode = strtolower($countryCode);

        // Return the domain from the mapping if it exists
        if (isset($this->googleDomains[$countryCode])) {
            return $this->googleDomains[$countryCode];
        }

        // Default to google.com for unknown country codes
        return 'google.com';
    }
}

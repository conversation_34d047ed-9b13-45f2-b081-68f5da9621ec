# WebSearchCountryCodeLeadsJob Testing Guide

## Overview

This guide explains how to test the `WebSearchCountryCodeLeadsJob` with its new time range and parameter ordering features.

## Running Tests

### PestPHP Tests

The job includes comprehensive PestPHP tests that verify:

- Constructor parameter validation
- Time range validation and normalization
- Default parameter behavior
- Queue integration
- Dispatch method functionality

**Run the tests:**
```bash
# Run all WebSearch job tests
./vendor/bin/pest tests/Unit/WebSearchJobBasicTest.php

# Run all tests
./vendor/bin/pest
```

### Manual Testing with Tinker

You can test the job manually using Laravel Tinker:

```bash
php artisan tinker
```

**Test basic instantiation:**
```php
// Test default parameters (week + default engines)
$job = new App\Jobs\WebSearchCountryCodeLeadsJob();

// Test with time range only
$job = new App\Jobs\WebSearchCountryCodeLeadsJob('day');

// Test with time range and engines
$job = new App\Jobs\WebSearchCountryCodeLeadsJob('week', ['google']);
```

**Test dispatch methods:**
```php
// Test default dispatch (week + default engines)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch();

// Test time range only (your requested pattern)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week');

// Test time range + engines (your requested pattern)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week', ['bing', 'google']);
```

**Test queue integration:**
```php
// Enable queue monitoring
Queue::fake();

// Dispatch jobs
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch();
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week');
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);

// Verify jobs were queued
Queue::assertPushed(App\Jobs\WebSearchCountryCodeLeadsJob::class, 3);
```

## Test Cases Covered

### 1. Parameter Order Tests
- ✅ `dispatch()` - uses defaults (week, ['bing', 'google'])
- ✅ `dispatch('week')` - time range only, default engines
- ✅ `dispatch('week', ['google'])` - time range + specific engines
- ✅ `dispatch('day', ['bing'])` - different combinations

### 2. Time Range Validation Tests
- ✅ Valid ranges: 'day', 'week', 'month'
- ✅ Invalid ranges default to 'week'
- ✅ Case normalization: 'WEEK' → 'week'
- ✅ Whitespace trimming: '  week  ' → 'week'

### 3. Default Behavior Tests
- ✅ Default time range is 'week' (7 days)
- ✅ Default engines are ['bing', 'google']
- ✅ Week is used as fallback for invalid ranges

### 4. Queue Integration Tests
- ✅ Jobs can be dispatched to queue
- ✅ Jobs maintain correct parameters when queued
- ✅ Multiple dispatch patterns work correctly

## Expected Behavior

### Time Range Mapping
- **'day'** → Current day only
- **'week'** → Last 7 days (newest first) [DEFAULT]
- **'month'** → Last 30 days (newest first)

### Search Engine Parameters
The job automatically configures correct API parameters:

**Google:**
- `day`: `tbs=qdr:d`
- `week`: `tbs=qdr:w`
- `month`: `tbs=qdr:m`

**Bing:**
- `day`: `time=day`
- `week`: `time=week`
- `month`: `time=month`

## Troubleshooting

### Common Issues

**Issue**: Tests hang or don't complete
**Solution**: The job may be trying to make actual API calls. Ensure you're testing in a controlled environment.

**Issue**: Invalid time range warnings
**Solution**: This is expected behavior. Invalid ranges automatically default to 'week'.

**Issue**: Queue not processing jobs
**Solution**: Make sure to run `php artisan queue:work` to process queued jobs.

### Debug Commands

```php
// Check job properties using reflection
$job = new App\Jobs\WebSearchCountryCodeLeadsJob('week', ['google']);
$reflection = new ReflectionClass($job);

// Check time range
$timeRangeProperty = $reflection->getProperty('timeRange');
$timeRangeProperty->setAccessible(true);
echo $timeRangeProperty->getValue($job); // Should output: week

// Check search engines
$enginesProperty = $reflection->getProperty('searchEngines');
$enginesProperty->setAccessible(true);
var_dump($enginesProperty->getValue($job)); // Should output: ['google']
```

## Success Criteria

Your implementation is working correctly if:

1. ✅ `WebSearchCountryCodeLeadsJob::dispatch('week', ['bing', 'google'])` works
2. ✅ `WebSearchCountryCodeLeadsJob::dispatch('week')` works
3. ✅ Default time range is 'week' (7 days)
4. ✅ Jobs can be queued and processed
5. ✅ Time range validation works correctly
6. ✅ Search engine parameters are configured properly

## Next Steps

After testing, you can:

1. **Run the job**: `WebSearchCountryCodeLeadsJob::dispatch('week')`
2. **Process queue**: `php artisan queue:work`
3. **Monitor logs**: Check Laravel logs for job execution details
4. **Verify results**: Check your leads database for new entries

The job will now search the last 7 days by default and return newest results first, exactly as you requested!

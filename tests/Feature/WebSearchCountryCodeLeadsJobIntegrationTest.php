<?php

use App\Jobs\WebSearchCountryCodeLeadsJob;
use Illuminate\Support\Facades\Queue;

describe('WebSearchCountryCodeLeadsJob Integration', function () {

    it('can be queued with default parameters', function () {
        Queue::fake();

        WebSearchCountryCodeLeadsJob::dispatch();

        Queue::assertPushed(WebSearchCountryCodeLeadsJob::class, 1);
    });

    it('can be queued with custom time range', function () {
        Queue::fake();

        WebSearchCountryCodeLeadsJob::dispatch('day');

        Queue::assertPushed(WebSearchCountryCodeLeadsJob::class, 1);
    });

    it('can be queued with custom engines', function () {
        Queue::fake();

        WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);

        Queue::assertPushed(WebSearchCountryCodeLeadsJob::class, 1);
    });

    it('supports the requested dispatch patterns', function () {
        Queue::fake();

        // Test the patterns you requested
        WebSearchCountryCodeLeadsJob::dispatch('week', ['bing', 'google']);
        WebSearchCountryCodeLeadsJob::dispatch('week');

        Queue::assertPushed(WebSearchCountryCodeLeadsJob::class, 2);
    });

});

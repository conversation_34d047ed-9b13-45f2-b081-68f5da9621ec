<?php

use App\Jobs\WebSearchCountryCodeLeadsJob;

describe('WebSearchCountryCodeLeadsJob', function () {

    it('can be instantiated with default parameters', function () {
        $job = new WebSearchCountryCodeLeadsJob;

        expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
    });

    it('can be instantiated with time range parameter', function () {
        $job = new WebSearchCountryCodeLeadsJob('day');

        expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
    });

    it('can be instantiated with time range and engines', function () {
        $job = new WebSearchCountryCodeLeadsJob('week', ['google']);

        expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
    });

    it('has default time range of week', function () {
        $job = new WebSearchCountryCodeLeadsJob;

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('week');
    });

    it('has default search engines', function () {
        $job = new WebSearchCountryCodeLeadsJob;

        $reflection = new ReflectionClass($job);
        $searchEnginesProperty = $reflection->getProperty('searchEngines');
        $searchEnginesProperty->setAccessible(true);

        expect($searchEnginesProperty->getValue($job))->toBe(['bing', 'google']);
    });

    it('accepts custom time range', function () {
        $job = new WebSearchCountryCodeLeadsJob('day');

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('day');
    });

    it('accepts custom search engines', function () {
        $job = new WebSearchCountryCodeLeadsJob('week', ['google']);

        $reflection = new ReflectionClass($job);
        $searchEnginesProperty = $reflection->getProperty('searchEngines');
        $searchEnginesProperty->setAccessible(true);

        expect($searchEnginesProperty->getValue($job))->toBe(['google']);
    });

    it('validates time ranges correctly', function () {
        $validRanges = ['day', 'week', 'month'];

        foreach ($validRanges as $range) {
            $job = new WebSearchCountryCodeLeadsJob($range);

            $reflection = new ReflectionClass($job);
            $timeRangeProperty = $reflection->getProperty('timeRange');
            $timeRangeProperty->setAccessible(true);

            expect($timeRangeProperty->getValue($job))->toBe($range);
        }
    });

    it('normalizes time range case', function () {
        $job = new WebSearchCountryCodeLeadsJob('WEEK');

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('week');
    });

    it('can be dispatched with different parameter combinations', function () {
        expect(function () {
            WebSearchCountryCodeLeadsJob::dispatch();
            WebSearchCountryCodeLeadsJob::dispatch('week');
            WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);
            WebSearchCountryCodeLeadsJob::dispatch('month', ['bing', 'google']);
        })->not->toThrow(Exception::class);
    });
});

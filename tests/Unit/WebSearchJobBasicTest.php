<?php

use App\Jobs\WebSearchCountryCodeLeadsJob;

it('can create WebSearchCountryCodeLeadsJob with default parameters', function () {
    $job = new WebSearchCountryCodeLeadsJob;

    expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
});

it('can create WebSearchCountryCodeLeadsJob with time range', function () {
    $job = new WebSearchCountryCodeLeadsJob('day');

    expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
});

it('can create WebSearchCountryCodeLeadsJob with time range and engines', function () {
    $job = new WebSearchCountryCodeLeadsJob('week', ['google']);

    expect($job)->toBeInstanceOf(WebSearchCountryCodeLeadsJob::class);
});

it('can dispatch WebSearchCountryCodeLeadsJob', function () {
    expect(function () {
        WebSearchCountryCodeLeadsJob::dispatch();
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchCountryCodeLeadsJob with time range', function () {
    expect(function () {
        WebSearchCountryCodeLeadsJob::dispatch('week');
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchCountryCodeLeadsJob with time range and engines', function () {
    expect(function () {
        WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);
    })->not->toThrow(Exception::class);
});
